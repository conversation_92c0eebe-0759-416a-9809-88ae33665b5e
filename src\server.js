import express from 'express';
import { renderToString } from 'vue/server-renderer';
import { createApp } from './main';

const server = express()
server.use(express.static('.'))

server.get('/', (req, res) => {
  const { app } = createApp()
   renderToString(app).then((html) => {
    res.send(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Vue SSR Example</title>
        </head>
        <body>
          <div id="app">${html}</div>
        </body>
      </html>
    `)
  })
})

server.listen(3000, () => {
  console.log('Server is running on http://localhost:3000')
})