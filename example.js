import express from 'express'

import { createSSRApp } from "vue";
import { renderToString } from 'vue/server-renderer'

const app = createSSRApp({
  data: () => ({ count: 1 }),
  template: `<button @click="count++">{{ count }}</button>`
})

const server = express()

server.get('/', (req, res) => {
   renderToString(app).then((html) => {
    res.send(`
      <DOCTYPE html>
      <html>
        <head>
          <title>Vue SSR Example</title>
        </head>
        <body>
          <div id="app">${html}</div>
        </body>
      </html>
    `)
     })
})

server.listen(3000, () => {
  console.log('Server is listening at http://localhost:3000')
})